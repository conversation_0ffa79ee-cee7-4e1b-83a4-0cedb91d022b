#!/usr/bin/env python3
"""
Validation script for functional activation files created by create_activations.sh

This script checks if the .pt and .json files are complete and uncorrupted.
"""

import os
import sys
import json
import torch
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def validate_pt_file(pt_path: str) -> Tuple[bool, Dict[str, any], List[str]]:
    """
    Validate a .pt functional activations file.
    
    Returns:
        (is_valid, file_info, errors)
    """
    errors = []
    file_info = {}
    
    try:
        # Check if file exists and has reasonable size
        if not os.path.exists(pt_path):
            errors.append(f"File does not exist: {pt_path}")
            return False, file_info, errors
            
        file_size = os.path.getsize(pt_path)
        file_info['file_size_mb'] = file_size / (1024 * 1024)
        
        if file_size == 0:
            errors.append("File is empty")
            return False, file_info, errors
            
        if file_size < 1024:  # Less than 1KB seems too small
            errors.append(f"File seems too small: {file_size} bytes")
            
        # Try to load the file
        try:
            data = torch.load(pt_path, map_location='cpu')
        except Exception as e:
            errors.append(f"Failed to load PyTorch file: {e}")
            return False, file_info, errors
            
        # Check expected keys based on save_results method
        expected_keys = {
            'mi_scores', 'expectation', 'scheming_features', 'truthful_features', 
            'top_k_features', 'mi_metadata'
        }
        optional_keys = {
            'z_scheming', 'z_truthful', 'scheming_indices', 'truthful_indices', 
            'functional_metadata'
        }
        
        missing_required = expected_keys - set(data.keys())
        if missing_required:
            errors.append(f"Missing required keys: {missing_required}")
            
        # Check if functional activations are present
        has_functional = all(key in data for key in optional_keys)
        file_info['has_functional_activations'] = has_functional
        
        if not has_functional:
            missing_functional = optional_keys - set(data.keys())
            errors.append(f"Missing functional activation keys: {missing_functional}")
            
        # Validate tensor shapes and properties
        for key, tensor in data.items():
            if isinstance(tensor, torch.Tensor):
                file_info[f'{key}_shape'] = list(tensor.shape)
                file_info[f'{key}_dtype'] = str(tensor.dtype)
                
                # Check for NaN or infinite values
                if torch.isnan(tensor).any():
                    errors.append(f"Tensor {key} contains NaN values")
                if torch.isinf(tensor).any():
                    errors.append(f"Tensor {key} contains infinite values")
                    
                # Check if tensor is empty
                if tensor.numel() == 0:
                    errors.append(f"Tensor {key} is empty")
                    
        # Validate metadata
        if 'mi_metadata' in data:
            metadata = data['mi_metadata']
            file_info['mi_metadata'] = metadata
            
            # Check for reasonable values
            if 'top_k' in metadata and metadata['top_k'] <= 0:
                errors.append("Invalid top_k value in metadata")
                
        if 'functional_metadata' in data:
            func_metadata = data['functional_metadata']
            file_info['functional_metadata'] = func_metadata
            
            # Check norms are reasonable
            for norm_key in ['scheming_norm', 'truthful_norm']:
                if norm_key in func_metadata:
                    norm_val = func_metadata[norm_key]
                    if norm_val <= 0 or norm_val > 1000:  # Reasonable range check
                        errors.append(f"Suspicious {norm_key} value: {norm_val}")
                        
    except Exception as e:
        errors.append(f"Unexpected error validating PT file: {e}")
        return False, file_info, errors
        
    is_valid = len(errors) == 0
    return is_valid, file_info, errors

def validate_json_file(json_path: str) -> Tuple[bool, Dict[str, any], List[str]]:
    """
    Validate a .json configuration file.
    
    Returns:
        (is_valid, file_info, errors)
    """
    errors = []
    file_info = {}
    
    try:
        if not os.path.exists(json_path):
            errors.append(f"File does not exist: {json_path}")
            return False, file_info, errors
            
        file_size = os.path.getsize(json_path)
        file_info['file_size_bytes'] = file_size
        
        if file_size == 0:
            errors.append("JSON file is empty")
            return False, file_info, errors
            
        # Try to load and parse JSON
        try:
            with open(json_path, 'r') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON format: {e}")
            return False, file_info, errors
        except Exception as e:
            errors.append(f"Failed to read JSON file: {e}")
            return False, file_info, errors
            
        # Check expected configuration keys
        expected_keys = {
            'model_name', 'sae_path', 'target_layer', 'steering_alpha', 
            'top_k_proportion', 'activations_file', 'timestamp'
        }
        
        missing_keys = expected_keys - set(data.keys())
        if missing_keys:
            errors.append(f"Missing configuration keys: {missing_keys}")
            
        # Validate specific values
        if 'target_layer' in data:
            layer = data['target_layer']
            if not isinstance(layer, int) or layer < 0 or layer > 50:
                errors.append(f"Invalid target_layer: {layer}")
                
        if 'steering_alpha' in data:
            alpha = data['steering_alpha']
            if not isinstance(alpha, (int, float)) or alpha <= 0:
                errors.append(f"Invalid steering_alpha: {alpha}")
                
        if 'top_k_proportion' in data:
            prop = data['top_k_proportion']
            if not isinstance(prop, (int, float)) or prop <= 0 or prop > 1:
                errors.append(f"Invalid top_k_proportion: {prop}")
                
        file_info['config_data'] = data
        
    except Exception as e:
        errors.append(f"Unexpected error validating JSON file: {e}")
        return False, file_info, errors
        
    is_valid = len(errors) == 0
    return is_valid, file_info, errors

def validate_layer_files(activations_dir: str, layer: int) -> Tuple[bool, Dict[str, any], List[str]]:
    """Validate both .pt and .json files for a specific layer."""
    errors = []
    file_info = {'layer': layer}
    
    # Find files for this layer
    pt_files = []
    json_files = []
    
    for filename in os.listdir(activations_dir):
        if f'_L{layer}_' in filename:
            if filename.endswith('.pt'):
                pt_files.append(filename)
            elif filename.endswith('.json'):
                json_files.append(filename)
                
    if len(pt_files) == 0:
        errors.append(f"No .pt file found for layer {layer}")
    elif len(pt_files) > 1:
        errors.append(f"Multiple .pt files found for layer {layer}: {pt_files}")
    else:
        pt_path = os.path.join(activations_dir, pt_files[0])
        pt_valid, pt_info, pt_errors = validate_pt_file(pt_path)
        file_info['pt_file'] = pt_files[0]
        file_info['pt_info'] = pt_info
        if not pt_valid:
            errors.extend([f"PT file error: {e}" for e in pt_errors])
            
    if len(json_files) == 0:
        errors.append(f"No .json file found for layer {layer}")
    elif len(json_files) > 1:
        errors.append(f"Multiple .json files found for layer {layer}: {json_files}")
    else:
        json_path = os.path.join(activations_dir, json_files[0])
        json_valid, json_info, json_errors = validate_json_file(json_path)
        file_info['json_file'] = json_files[0]
        file_info['json_info'] = json_info
        if not json_valid:
            errors.extend([f"JSON file error: {e}" for e in json_errors])
            
    is_valid = len(errors) == 0
    return is_valid, file_info, errors

def main():
    parser = argparse.ArgumentParser(description='Validate functional activation files')
    parser.add_argument('activations_dir', help='Directory containing activation files')
    parser.add_argument('--layers', type=int, nargs='+', default=list(range(32)), 
                       help='Layers to validate (default: 0-31)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.activations_dir):
        logger.error(f"Directory does not exist: {args.activations_dir}")
        sys.exit(1)
        
    logger.info(f"Validating activation files in: {args.activations_dir}")
    logger.info(f"Checking layers: {args.layers}")
    
    valid_layers = []
    invalid_layers = []
    layer_details = {}
    
    for layer in args.layers:
        logger.info(f"Validating layer {layer}...")
        is_valid, file_info, errors = validate_layer_files(args.activations_dir, layer)
        
        layer_details[layer] = {
            'valid': is_valid,
            'info': file_info,
            'errors': errors
        }
        
        if is_valid:
            valid_layers.append(layer)
            if args.verbose:
                logger.info(f"  ✅ Layer {layer}: VALID")
        else:
            invalid_layers.append(layer)
            logger.warning(f"  ❌ Layer {layer}: INVALID")
            for error in errors:
                logger.warning(f"    - {error}")
                
    # Summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    print(f"Total layers checked: {len(args.layers)}")
    print(f"✅ Valid layers: {len(valid_layers)}")
    print(f"❌ Invalid layers: {len(invalid_layers)}")
    
    if valid_layers:
        print(f"\n✅ VALID LAYERS: {valid_layers}")
        
    if invalid_layers:
        print(f"\n❌ INVALID LAYERS: {invalid_layers}")
        
    if args.verbose and valid_layers:
        print(f"\n📊 SAMPLE FILE INFO (Layer {valid_layers[0]}):")
        sample_info = layer_details[valid_layers[0]]['info']
        if 'pt_info' in sample_info:
            pt_info = sample_info['pt_info']
            print(f"  PT file size: {pt_info.get('file_size_mb', 0):.2f} MB")
            print(f"  Has functional activations: {pt_info.get('has_functional_activations', False)}")
            if 'mi_metadata' in pt_info:
                metadata = pt_info['mi_metadata']
                print(f"  Top-k features: {metadata.get('top_k', 'unknown')}")
                print(f"  Scheming examples: {metadata.get('num_scheming_examples', 'unknown')}")
                print(f"  Truthful examples: {metadata.get('num_truthful_examples', 'unknown')}")
                
    # Exit with error code if any layers are invalid
    if invalid_layers:
        sys.exit(1)
    else:
        print(f"\n🎉 All {len(valid_layers)} layers are valid!")
        sys.exit(0)

if __name__ == '__main__':
    main()
