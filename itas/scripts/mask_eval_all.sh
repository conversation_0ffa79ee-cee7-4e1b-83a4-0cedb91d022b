# Check if layers are provided as arguments
if [[ $# -gt 0 ]]; then
    # Use provided layers
    LAYERS=("$@")
else
    # Default layers if none provided
    LAYERS=(6 7 9 12 13 19 20 24 27)
fi

echo "Evaluating layers: ${LAYERS[@]}"

for i in "${LAYERS[@]}"; do
    echo "Processing layer $i..."
    bash mask_eval.sh /data_x/junkim100/projects/scheming_sae/itas/results/meta-llama-Llama-3.1-8B-Instruct_alpha0.1/mask_responses/layer_$i/
done
